package org.yixz.common.util;

import cn.hutool.core.date.DateUtil;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.properties.VideoDownloadProperties;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Date;

public class FileUtil {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(FileUtil.class);

    public static String generateVideoDownloadDir(String platform, Boolean isTmp, String filename) {
        String separator = FileSystems.getDefault().getSeparator();
        String datepath = DateUtil.format(new Date(), "yyyyMMdd");
        // 拼接平台
        VideoDownloadProperties downloadProperties = SpringContextUtil.getBean(VideoDownloadProperties.class);
        String savePath = downloadProperties.getSaveDir() ;
        if (isTmp){
            savePath = downloadProperties.getTempDir();
        }
        savePath += separator + platform + separator + datepath + separator + filename;
        File file = new File(savePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        return savePath;
    }

    /**
     * 删除单个文件
     *
     * @param fileName 被删除的文件名
     */
    public static void deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && file.isFile()) {
            try {
                file.delete();
                log.debug("文件已清理: {}",  fileName);
            }catch (Exception e){
                log.warn("清理文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 清理临时文件
     */
    public static void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("临时文件已清理: {}", file.getAbsolutePath());
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 创建临时文件（基于InputStream的核心方法）
     *
     * @param inputStream 输入流
     * @param prefix 临时文件前缀
     * @param extension 文件扩展名（包含点号，如".mp4"）
     * @return 创建的临时文件
     * @throws IOException IO异常
     */
    public static File createTempFile(InputStream inputStream, String prefix, String extension) throws IOException {
        File tempFile = File.createTempFile(prefix, extension);

        try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        return tempFile;
    }

    /**
     * 创建临时文件（MultipartFile版本）
     *
     * @param multipartFile 上传的文件
     * @param prefix 临时文件前缀
     * @return 创建的临时文件
     * @throws IOException IO异常
     */
    public static File createTempFile(MultipartFile multipartFile, String prefix) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        try (InputStream inputStream = multipartFile.getInputStream()) {
            return createTempFile(inputStream, prefix, extension);
        }
    }

    /**
     * 创建临时文件（File版本）
     *
     * @param file 源文件
     * @param prefix 临时文件前缀
     * @return 创建的临时文件
     * @throws IOException IO异常
     */
    public static File createTempFile(File file, String prefix) throws IOException {
        String originalFilename = file.getName();
        String extension = "";
        if (originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        try (InputStream inputStream = new FileInputStream(file)) {
            return createTempFile(inputStream, prefix, extension);
        }
    }

    /**
     * 移动文件到指定位置
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 移动是否成功
     * @throws IOException IO异常
     */
    public static boolean moveFile(String sourcePath, String targetPath) throws IOException {
        return moveFile(new File(sourcePath), new File(targetPath), false);
    }

    /**
     * 移动文件到指定位置（支持覆盖选项）
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖已存在的目标文件
     * @return 移动是否成功
     * @throws IOException IO异常
     */
    public static boolean moveFile(String sourcePath, String targetPath, boolean overwrite) throws IOException {
        return moveFile(new File(sourcePath), new File(targetPath), overwrite);
    }

    /**
     * 移动文件到指定位置（File对象版本）
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 移动是否成功
     * @throws IOException IO异常
     */
    public static boolean moveFile(File sourceFile, File targetFile) throws IOException {
        return moveFile(sourceFile, targetFile, false);
    }

    /**
     * 移动文件到指定位置（File对象版本，支持覆盖选项）
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @param overwrite 是否覆盖已存在的目标文件
     * @return 移动是否成功
     * @throws IOException IO异常
     */
    public static boolean moveFile(File sourceFile, File targetFile, boolean overwrite) throws IOException {
        if (sourceFile == null || targetFile == null) {
            throw new IllegalArgumentException("源文件和目标文件不能为null");
        }

        if (!sourceFile.exists()) {
            throw new IOException("源文件不存在: " + sourceFile.getAbsolutePath());
        }

        if (!sourceFile.isFile()) {
            throw new IOException("源路径不是文件: " + sourceFile.getAbsolutePath());
        }

        try {
            Path sourcePath = sourceFile.toPath();
            Path targetPath = targetFile.toPath();

            // 确保目标目录存在
            Path targetDir = targetPath.getParent();
            if (targetDir != null && !Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
                log.debug("创建目标目录: {}", targetDir);
            }

            // 检查目标文件是否已存在
            if (Files.exists(targetPath) && !overwrite) {
                throw new IOException("目标文件已存在且不允许覆盖: " + targetFile.getAbsolutePath());
            }

            // 执行文件移动
            if (overwrite) {
                Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            } else {
                Files.move(sourcePath, targetPath);
            }

            log.info("文件移动成功: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath());
            return true;

        } catch (IOException e) {
            log.error("文件移动失败: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath(), e);
            throw e;
        }
    }

    /**
     * 复制文件到指定位置
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 复制是否成功
     * @throws IOException IO异常
     */
    public static boolean copyFile(String sourcePath, String targetPath) throws IOException {
        return copyFile(new File(sourcePath), new File(targetPath), false);
    }

    /**
     * 复制文件到指定位置（支持覆盖选项）
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @param overwrite 是否覆盖已存在的目标文件
     * @return 复制是否成功
     * @throws IOException IO异常
     */
    public static boolean copyFile(String sourcePath, String targetPath, boolean overwrite) throws IOException {
        return copyFile(new File(sourcePath), new File(targetPath), overwrite);
    }

    /**
     * 复制文件到指定位置（File对象版本）
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 复制是否成功
     * @throws IOException IO异常
     */
    public static boolean copyFile(File sourceFile, File targetFile) throws IOException {
        return copyFile(sourceFile, targetFile, false);
    }

    /**
     * 复制文件到指定位置（File对象版本，支持覆盖选项）
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @param overwrite 是否覆盖已存在的目标文件
     * @return 复制是否成功
     * @throws IOException IO异常
     */
    public static boolean copyFile(File sourceFile, File targetFile, boolean overwrite) throws IOException {
        if (sourceFile == null || targetFile == null) {
            throw new IllegalArgumentException("源文件和目标文件不能为null");
        }

        if (!sourceFile.exists()) {
            throw new IOException("源文件不存在: " + sourceFile.getAbsolutePath());
        }

        if (!sourceFile.isFile()) {
            throw new IOException("源路径不是文件: " + sourceFile.getAbsolutePath());
        }

        try {
            Path sourcePath = sourceFile.toPath();
            Path targetPath = targetFile.toPath();

            // 确保目标目录存在
            Path targetDir = targetPath.getParent();
            if (targetDir != null && !Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
                log.debug("创建目标目录: {}", targetDir);
            }

            // 检查目标文件是否已存在
            if (Files.exists(targetPath) && !overwrite) {
                throw new IOException("目标文件已存在且不允许覆盖: " + targetFile.getAbsolutePath());
            }

            // 执行文件复制
            if (overwrite) {
                Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            } else {
                Files.copy(sourcePath, targetPath);
            }

            log.info("文件复制成功: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath());
            return true;

        } catch (IOException e) {
            log.error("文件复制失败: {} -> {}", sourceFile.getAbsolutePath(), targetFile.getAbsolutePath(), e);
            throw e;
        }
    }
}
