package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.converter.SubtitleExtractor;
import org.yixz.common.converter.SubtitleExtractionResult;
import org.yixz.common.exception.AudioConversionException;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字幕提取通用工具类
 * 提供简化的字幕提取API，封装常用的字幕处理操作
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
public class SubtitleUtils {
    
    /**
     * 支持的视频文件扩展名
     */
    private static final String[] SUPPORTED_VIDEO_EXTENSIONS = {
        ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp", ".ts"
    };
    
    /**
     * 支持的字幕格式
     */
    private static final String[] SUPPORTED_SUBTITLE_FORMATS = {
        "SRT", "VTT", "ASS", "SSA", "SUB", "IDX"
    };
    
    /**
     * 从视频文件中提取所有字幕（通用方法）
     * 
     * @param videoFile 视频文件
     * @return 字幕提取结果
     * @throws AudioConversionException 提取失败时抛出
     */
    public static SubtitleExtractionResult extractAllSubtitles(File videoFile) {
        validateVideoFile(videoFile);
        
        try {
            log.info("开始提取字幕，文件: {}", videoFile.getName());
            
            // 检查字幕提取功能是否可用
            if (!SubtitleExtractor.isSubtitleExtractionSupported()) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitles(videoFile);
            
        } catch (Exception e) {
            log.error("字幕提取失败: {}", e.getMessage());
            throw new AudioConversionException("字幕提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从视频文件中提取所有字幕（MultipartFile版本）
     * 
     * @param videoFile 视频文件
     * @return 字幕提取结果
     * @throws AudioConversionException 提取失败时抛出
     */
    public static SubtitleExtractionResult extractAllSubtitles(MultipartFile videoFile) {
        validateVideoFile(videoFile);
        
        try {
            log.info("开始提取字幕，文件: {}", videoFile.getOriginalFilename());
            
            // 检查字幕提取功能是否可用
            if (!SubtitleExtractor.isSubtitleExtractionSupported()) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitles(videoFile);
            
        } catch (Exception e) {
            log.error("字幕提取失败: {}", e.getMessage());
            throw new AudioConversionException("字幕提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从视频文件中提取指定字幕轨道
     * 
     * @param videoFile 视频文件
     * @param trackIndex 字幕轨道索引（从0开始）
     * @return 字幕提取结果
     * @throws AudioConversionException 提取失败时抛出
     */
    public static SubtitleExtractionResult extractSubtitleTrack(File videoFile, int trackIndex) {
        validateVideoFile(videoFile);
        validateTrackIndex(trackIndex);
        
        try {
            log.info("开始提取字幕轨道，文件: {}, 轨道索引: {}", videoFile.getName(), trackIndex);
            
            // 检查字幕提取功能是否可用
            if (!SubtitleExtractor.isSubtitleExtractionSupported()) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitleTrack(videoFile, trackIndex);
            
        } catch (Exception e) {
            log.error("字幕轨道提取失败: {}", e.getMessage());
            throw new AudioConversionException("字幕轨道提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从视频文件中提取指定字幕轨道（MultipartFile版本）
     *
     * @param videoFile 视频文件
     * @param trackIndex 字幕轨道索引（从0开始）
     * @return 字幕提取结果
     * @throws AudioConversionException 提取失败时抛出
     */
    public static SubtitleExtractionResult extractSubtitleTrack(MultipartFile videoFile, int trackIndex) {
        validateVideoFile(videoFile);
        validateTrackIndex(trackIndex);

        try {
            log.info("开始提取字幕轨道，文件: {}, 轨道索引: {}", videoFile.getOriginalFilename(), trackIndex);

            // 检查字幕提取功能是否可用
            if (!SubtitleExtractor.isSubtitleExtractionSupported()) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }

            return SubtitleExtractor.extractSubtitleTrack(videoFile, trackIndex);

        } catch (Exception e) {
            log.error("字幕轨道提取失败: {}", e.getMessage());
            throw new AudioConversionException("字幕轨道提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量提取字幕（处理多个视频文件）
     * 
     * @param videoFiles 视频文件列表
     * @return 字幕提取结果列表
     */
    public static List<SubtitleExtractionResult> batchExtractSubtitles(List<File> videoFiles) {
        if (videoFiles == null || videoFiles.isEmpty()) {
            throw new IllegalArgumentException("视频文件列表不能为空");
        }
        
        List<SubtitleExtractionResult> results = new ArrayList<>();
        
        for (File videoFile : videoFiles) {
            try {
                SubtitleExtractionResult result = extractAllSubtitles(videoFile);
                results.add(result);
                log.info("批量处理成功: {}", videoFile.getName());
            } catch (Exception e) {
                log.error("批量处理失败: {}, 错误: {}", videoFile.getName(), e.getMessage());
                // 创建失败结果
                SubtitleExtractionResult failedResult = new SubtitleExtractionResult();
                failedResult.setSuccess(false);
                failedResult.setMessage("提取失败: " + e.getMessage());
                results.add(failedResult);
            }
        }
        
        log.info("批量字幕提取完成，处理文件数: {}, 成功数: {}", 
                videoFiles.size(), 
                results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        
        return results;
    }
    
    /**
     * 批量提取字幕（MultipartFile版本）
     * 
     * @param videoFiles 视频文件列表
     * @return 字幕提取结果列表
     */
    public static List<SubtitleExtractionResult> batchExtractSubtitles(MultipartFile[] videoFiles) {
        if (videoFiles == null || videoFiles.length == 0) {
            throw new IllegalArgumentException("视频文件列表不能为空");
        }
        
        List<SubtitleExtractionResult> results = new ArrayList<>();
        
        for (MultipartFile videoFile : videoFiles) {
            try {
                SubtitleExtractionResult result = extractAllSubtitles(videoFile);
                results.add(result);
                log.info("批量处理成功: {}", videoFile.getOriginalFilename());
            } catch (Exception e) {
                log.error("批量处理失败: {}, 错误: {}", videoFile.getOriginalFilename(), e.getMessage());
                // 创建失败结果
                SubtitleExtractionResult failedResult = new SubtitleExtractionResult();
                failedResult.setSuccess(false);
                failedResult.setMessage("提取失败: " + e.getMessage());
                results.add(failedResult);
            }
        }
        
        log.info("批量字幕提取完成，处理文件数: {}, 成功数: {}", 
                videoFiles.length, 
                results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        
        return results;
    }
    
    /**
     * 从目录中批量提取所有视频文件的字幕
     * 
     * @param directory 目录路径
     * @param recursive 是否递归搜索子目录
     * @return 字幕提取结果列表
     */
    public static List<SubtitleExtractionResult> extractSubtitlesFromDirectory(Path directory, boolean recursive) {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            throw new IllegalArgumentException("目录不存在或不是有效目录: " + directory);
        }
        
        try {
            List<File> videoFiles = findVideoFiles(directory, recursive);
            log.info("在目录 {} 中找到 {} 个视频文件", directory, videoFiles.size());
            
            return batchExtractSubtitles(videoFiles);
            
        } catch (IOException e) {
            log.error("搜索视频文件失败: {}", e.getMessage());
            throw new AudioConversionException("搜索视频文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查视频文件是否包含字幕
     * 
     * @param videoFile 视频文件
     * @return 是否包含字幕
     */
    public static boolean hasSubtitles(File videoFile) {
        try {
            SubtitleExtractionResult result = extractAllSubtitles(videoFile);
            return result.isSuccess() && result.getSubtitleTrackCount() > 0;
        } catch (Exception e) {
            log.debug("检查字幕失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查视频文件是否包含字幕（MultipartFile版本）
     *
     * @param videoFile 视频文件
     * @return 是否包含字幕
     */
    public static boolean hasSubtitles(MultipartFile videoFile) {
        try {
            SubtitleExtractionResult result = extractAllSubtitles(videoFile);
            return result.isSuccess() && result.getSubtitleTrackCount() > 0;
        } catch (Exception e) {
            log.debug("检查字幕失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查视频文件是否包含字幕（InputStream版本）
     *
     * @param inputStream 视频文件输入流
     * @param filename 文件名（用于确定格式）
     * @return 是否包含字幕
     */
    public static boolean hasSubtitles(InputStream inputStream, String filename) {
        try {
            SubtitleExtractionResult result = extractAllSubtitles(inputStream, filename);
            return result.isSuccess() && result.getSubtitleTrackCount() > 0;
        } catch (Exception e) {
            log.debug("检查字幕失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 批量提取字幕（处理多个InputStream）
     *
     * @param inputSources 输入源列表（包含InputStream和对应的文件名）
     * @return 字幕提取结果列表
     */
    public static List<SubtitleExtractionResult> batchExtractSubtitles(List<VideoInputSource> inputSources) {
        if (inputSources == null || inputSources.isEmpty()) {
            throw new IllegalArgumentException("输入源列表不能为空");
        }

        List<SubtitleExtractionResult> results = new ArrayList<>();

        for (VideoInputSource inputSource : inputSources) {
            try {
                inputSource.validate();
                SubtitleExtractionResult result = SubtitleExtractor.extractSubtitles(inputSource);
                results.add(result);
                log.info("批量处理成功: {}", inputSource.getFilename());
            } catch (Exception e) {
                log.error("批量处理失败: {}, 错误: {}", inputSource.getFilename(), e.getMessage());
                // 创建失败结果
                SubtitleExtractionResult failedResult = new SubtitleExtractionResult();
                failedResult.setSuccess(false);
                failedResult.setMessage("提取失败: " + e.getMessage());
                results.add(failedResult);
            }
        }

        log.info("批量字幕提取完成，处理文件数: {}, 成功数: {}",
                inputSources.size(),
                results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());

        return results;
    }
    
    /**
     * 获取支持的视频文件扩展名
     * 
     * @return 支持的扩展名数组
     */
    public static String[] getSupportedVideoExtensions() {
        return SUPPORTED_VIDEO_EXTENSIONS.clone();
    }
    
    /**
     * 获取支持的字幕格式
     * 
     * @return 支持的字幕格式数组
     */
    public static String[] getSupportedSubtitleFormats() {
        return SUPPORTED_SUBTITLE_FORMATS.clone();
    }
    
    /**
     * 检查字幕提取功能是否可用
     * 
     * @return 是否可用
     */
    public static boolean isSubtitleExtractionAvailable() {
        return SubtitleExtractor.isSubtitleExtractionSupported();
    }
    
    /**
     * 获取首选的字幕提取器类型
     *
     * @return 提取器类型
     */
    public static String getPreferredExtractor() {
        return MediaConverterUtil.getPreferredSubtitleExtractor();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证视频文件（File版本）
     *
     * @param videoFile 视频文件
     * @throws IllegalArgumentException 文件无效时抛出
     */
    private static void validateVideoFile(File videoFile) {
        if (videoFile == null) {
            throw new IllegalArgumentException("视频文件不能为null");
        }

        if (!videoFile.exists()) {
            throw new IllegalArgumentException("视频文件不存在: " + videoFile.getAbsolutePath());
        }

        if (!videoFile.isFile()) {
            throw new IllegalArgumentException("路径不是文件: " + videoFile.getAbsolutePath());
        }

        if (videoFile.length() == 0) {
            throw new IllegalArgumentException("视频文件为空: " + videoFile.getAbsolutePath());
        }

        // 检查文件扩展名
        String fileName = videoFile.getName().toLowerCase();
        boolean isSupported = Arrays.stream(SUPPORTED_VIDEO_EXTENSIONS)
                .anyMatch(fileName::endsWith);

        if (!isSupported) {
            throw new IllegalArgumentException("不支持的视频文件格式: " + fileName +
                    "，支持的格式: " + Arrays.toString(SUPPORTED_VIDEO_EXTENSIONS));
        }
    }

    /**
     * 验证视频文件（MultipartFile版本）
     *
     * @param videoFile 视频文件
     * @throws IllegalArgumentException 文件无效时抛出
     */
    private static void validateVideoFile(MultipartFile videoFile) {
        if (videoFile == null) {
            throw new IllegalArgumentException("视频文件不能为null");
        }

        if (videoFile.isEmpty()) {
            throw new IllegalArgumentException("视频文件为空");
        }

        String originalFilename = videoFile.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("视频文件名不能为空");
        }

        // 检查文件扩展名
        String fileName = originalFilename.toLowerCase();
        boolean isSupported = Arrays.stream(SUPPORTED_VIDEO_EXTENSIONS)
                .anyMatch(fileName::endsWith);

        if (!isSupported) {
            throw new IllegalArgumentException("不支持的视频文件格式: " + fileName +
                    "，支持的格式: " + Arrays.toString(SUPPORTED_VIDEO_EXTENSIONS));
        }
    }

    /**
     * 验证轨道索引
     *
     * @param trackIndex 轨道索引
     * @throws IllegalArgumentException 索引无效时抛出
     */
    private static void validateTrackIndex(int trackIndex) {
        if (trackIndex < 0) {
            throw new IllegalArgumentException("字幕轨道索引不能为负数: " + trackIndex);
        }
    }

    /**
     * 在目录中查找视频文件
     *
     * @param directory 目录路径
     * @param recursive 是否递归搜索
     * @return 视频文件列表
     * @throws IOException 搜索失败时抛出
     */
    private static List<File> findVideoFiles(Path directory, boolean recursive) throws IOException {
        List<File> videoFiles = new ArrayList<>();

        if (recursive) {
            Files.walk(directory)
                    .filter(Files::isRegularFile)
                    .filter(path -> isVideoFile(path.getFileName().toString()))
                    .forEach(path -> videoFiles.add(path.toFile()));
        } else {
            Files.list(directory)
                    .filter(Files::isRegularFile)
                    .filter(path -> isVideoFile(path.getFileName().toString()))
                    .forEach(path -> videoFiles.add(path.toFile()));
        }

        return videoFiles;
    }

    /**
     * 检查文件是否为视频文件
     *
     * @param fileName 文件名
     * @return 是否为视频文件
     */
    private static boolean isVideoFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return Arrays.stream(SUPPORTED_VIDEO_EXTENSIONS)
                .anyMatch(lowerFileName::endsWith);
    }
}
