package org.yixz.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 字幕提取配置类
 * 统一管理字幕提取的配置参数
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Configuration
@ConfigurationProperties(prefix = "subtitle.extraction")
public class SubtitleConfig {
    
    /**
     * 默认输出格式
     */
    private String defaultOutputFormat = "SRT";
    
    /**
     * 是否启用批量处理
     */
    private boolean batchProcessingEnabled = true;
    
    /**
     * 批量处理最大文件数
     */
    private int maxBatchSize = 10;
    
    /**
     * 是否启用字幕内容验证
     */
    private boolean validationEnabled = true;
    
    /**
     * 临时文件保留时间（分钟）
     */
    private int tempFileRetentionMinutes = 30;
    
    /**
     * 是否自动清理临时文件
     */
    private boolean autoCleanupEnabled = true;
    
    /**
     * 字幕提取超时时间（秒）
     */
    private int extractionTimeoutSeconds = 300;
    
    /**
     * 支持的最大文件大小（MB）
     */
    private long maxFileSizeMB = 500;
    
    /**
     * 是否启用递归目录搜索
     */
    private boolean recursiveSearchEnabled = true;
    
    /**
     * 字幕质量设置
     */
    private QualitySettings quality = new QualitySettings();
    
    /**
     * 输出设置
     */
    private OutputSettings output = new OutputSettings();
    
    // Getter和Setter方法
    public String getDefaultOutputFormat() {
        return defaultOutputFormat;
    }
    
    public void setDefaultOutputFormat(String defaultOutputFormat) {
        this.defaultOutputFormat = defaultOutputFormat;
    }
    
    public boolean isBatchProcessingEnabled() {
        return batchProcessingEnabled;
    }
    
    public void setBatchProcessingEnabled(boolean batchProcessingEnabled) {
        this.batchProcessingEnabled = batchProcessingEnabled;
    }
    
    public int getMaxBatchSize() {
        return maxBatchSize;
    }
    
    public void setMaxBatchSize(int maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }
    
    public boolean isValidationEnabled() {
        return validationEnabled;
    }
    
    public void setValidationEnabled(boolean validationEnabled) {
        this.validationEnabled = validationEnabled;
    }
    
    public int getTempFileRetentionMinutes() {
        return tempFileRetentionMinutes;
    }
    
    public void setTempFileRetentionMinutes(int tempFileRetentionMinutes) {
        this.tempFileRetentionMinutes = tempFileRetentionMinutes;
    }
    
    public boolean isAutoCleanupEnabled() {
        return autoCleanupEnabled;
    }
    
    public void setAutoCleanupEnabled(boolean autoCleanupEnabled) {
        this.autoCleanupEnabled = autoCleanupEnabled;
    }
    
    public int getExtractionTimeoutSeconds() {
        return extractionTimeoutSeconds;
    }
    
    public void setExtractionTimeoutSeconds(int extractionTimeoutSeconds) {
        this.extractionTimeoutSeconds = extractionTimeoutSeconds;
    }
    
    public long getMaxFileSizeMB() {
        return maxFileSizeMB;
    }
    
    public void setMaxFileSizeMB(long maxFileSizeMB) {
        this.maxFileSizeMB = maxFileSizeMB;
    }
    
    public boolean isRecursiveSearchEnabled() {
        return recursiveSearchEnabled;
    }
    
    public void setRecursiveSearchEnabled(boolean recursiveSearchEnabled) {
        this.recursiveSearchEnabled = recursiveSearchEnabled;
    }
    
    public QualitySettings getQuality() {
        return quality;
    }
    
    public void setQuality(QualitySettings quality) {
        this.quality = quality;
    }
    
    public OutputSettings getOutput() {
        return output;
    }
    
    public void setOutput(OutputSettings output) {
        this.output = output;
    }
    
    /**
     * 字幕质量设置
     */
    public static class QualitySettings {
        /**
         * 是否保留原始格式
         */
        private boolean preserveOriginalFormat = true;
        
        /**
         * 字符编码
         */
        private String encoding = "UTF-8";
        
        /**
         * 是否移除样式信息
         */
        private boolean removeStyleInfo = false;
        
        public boolean isPreserveOriginalFormat() {
            return preserveOriginalFormat;
        }
        
        public void setPreserveOriginalFormat(boolean preserveOriginalFormat) {
            this.preserveOriginalFormat = preserveOriginalFormat;
        }
        
        public String getEncoding() {
            return encoding;
        }
        
        public void setEncoding(String encoding) {
            this.encoding = encoding;
        }
        
        public boolean isRemoveStyleInfo() {
            return removeStyleInfo;
        }
        
        public void setRemoveStyleInfo(boolean removeStyleInfo) {
            this.removeStyleInfo = removeStyleInfo;
        }
    }
    
    /**
     * 输出设置
     */
    public static class OutputSettings {
        /**
         * 输出目录
         */
        private String outputDirectory = "temp/subtitles";
        
        /**
         * 文件名模式
         */
        private String filenamePattern = "{original_name}_{track_index}.{format}";
        
        /**
         * 是否包含时间戳
         */
        private boolean includeTimestamp = false;
        
        public String getOutputDirectory() {
            return outputDirectory;
        }
        
        public void setOutputDirectory(String outputDirectory) {
            this.outputDirectory = outputDirectory;
        }
        
        public String getFilenamePattern() {
            return filenamePattern;
        }
        
        public void setFilenamePattern(String filenamePattern) {
            this.filenamePattern = filenamePattern;
        }
        
        public boolean isIncludeTimestamp() {
            return includeTimestamp;
        }
        
        public void setIncludeTimestamp(boolean includeTimestamp) {
            this.includeTimestamp = includeTimestamp;
        }
    }
    
    @Override
    public String toString() {
        return String.format("SubtitleConfig{defaultFormat='%s', batchEnabled=%s, maxBatchSize=%d, validationEnabled=%s}",
                defaultOutputFormat, batchProcessingEnabled, maxBatchSize, validationEnabled);
    }
}
