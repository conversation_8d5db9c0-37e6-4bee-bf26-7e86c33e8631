package org.yixz.common.converter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.util.FileUtil;
import org.yixz.common.util.MediaConverterUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * 字幕提取器
 * 基于JAVE2/FFmpeg实现字幕提取功能
 *
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
@Slf4j
public class SubtitleExtractor {

    /**
     * 从视频文件中提取所有字幕
     *
     * @param videoFile 视频文件
     * @return 字幕提取结果
     * @throws AudioConversionException 提取异常
     */
    public static SubtitleExtractionResult extractSubtitles(MultipartFile videoFile) throws AudioConversionException {
        if (videoFile == null || videoFile.isEmpty()) {
            throw new AudioConversionException("视频文件不能为空");
        }

        File tempSourceFile = null;

        try {
            long startTime = System.currentTimeMillis();

            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(videoFile, "subtitle_source_");

            // 检测视频信息（使用AudioConverter的方法）
            MediaSeparationResult.VideoInfo videoInfo = AudioConverter.detectVideoInfo(tempSourceFile);

            // 创建结果对象
            SubtitleExtractionResult result = new SubtitleExtractionResult();
            result.setSourceVideoInfo(videoInfo);
            result.setExtractorUsed("JAVE2");

            // 检查是否包含字幕
            if (!videoInfo.isHasSubtitles()) {
                result.setSuccess(true);
                result.setMessage("视频文件不包含字幕轨道");
                result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                return result;
            }

            // 选择提取器并提取字幕轨道
            List<SubtitleExtractionResult.SubtitleFile> subtitleFiles;
            String extractorUsed;

            if (isFFmpegAvailable()) {
                log.info("使用FFmpeg进行字幕提取");
                subtitleFiles = extractAllSubtitleTracksWithFFmpeg(tempSourceFile);
                extractorUsed = "FFmpeg";
            } else if (isJaveAvailable()) {
                log.info("使用JAVE2进行字幕提取（功能有限）");
                subtitleFiles = extractAllSubtitleTracksWithJave(tempSourceFile);
                extractorUsed = "JAVE2";
            } else {
                throw new AudioConversionException("没有可用的字幕提取器，请安装FFmpeg或确保JAVE2依赖正确");
            }

            long processingTime = System.currentTimeMillis() - startTime;

            // 设置结果
            result.setSuccess(true);
            result.setSubtitleFiles(subtitleFiles);
            result.setProcessingTimeMs(processingTime);
            result.setExtractorUsed(extractorUsed);
            result.setMessage(String.format("字幕提取成功，共提取 %d 个字幕轨道，耗时: %dms，使用: %s",
                    subtitleFiles.size(), processingTime, extractorUsed));

            log.info("字幕提取成功，共提取 {} 个轨道，耗时: {}ms，使用: {}", subtitleFiles.size(), processingTime, extractorUsed);

            return result;

        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("字幕提取失败", e);
            throw new AudioConversionException("字幕提取失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            if (tempSourceFile != null && tempSourceFile.exists()) {
                tempSourceFile.delete();
            }
        }
    }

    /**
     * 从视频文件中提取指定字幕轨道
     *
     * @param videoFile 视频文件
     * @param trackIndex 字幕轨道索引
     * @return 字幕提取结果
     * @throws AudioConversionException 提取异常
     */
    public static SubtitleExtractionResult extractSubtitleTrack(File videoFile, int trackIndex) throws AudioConversionException {
        if (videoFile == null || videoFile.length() == 0) {
            throw new AudioConversionException("视频文件不能为空");
        }

        File tempSourceFile = null;

        try {
            long startTime = System.currentTimeMillis();

            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(videoFile, "subtitle_source_");

            // 检测视频信息（使用AudioConverter的方法）
            MediaSeparationResult.VideoInfo videoInfo = AudioConverter.detectVideoInfo(tempSourceFile);

            // 创建结果对象
            SubtitleExtractionResult result = new SubtitleExtractionResult();
            result.setSourceVideoInfo(videoInfo);
            result.setExtractorUsed("JAVE2");

            // 检查是否包含字幕
            if (!videoInfo.isHasSubtitles()) {
                throw new AudioConversionException("视频文件不包含字幕轨道");
            }

            // 选择提取器并提取指定字幕轨道
            SubtitleExtractionResult.SubtitleFile subtitleFile;
            String extractorUsed;

            if (isFFmpegAvailable()) {
                log.info("使用FFmpeg提取字幕轨道 {}", trackIndex);
                subtitleFile = extractSingleSubtitleTrackWithFFmpeg(tempSourceFile, trackIndex);
                extractorUsed = "FFmpeg";
            } else if (isJaveAvailable()) {
                log.info("使用JAVE2提取字幕轨道 {}（功能有限）", trackIndex);
                subtitleFile = extractSingleSubtitleTrackWithJave(tempSourceFile, trackIndex);
                extractorUsed = "JAVE2";
            } else {
                throw new AudioConversionException("没有可用的字幕提取器，请安装FFmpeg或确保JAVE2依赖正确");
            }

            long processingTime = System.currentTimeMillis() - startTime;

            // 设置结果
            result.setSuccess(true);
            result.addSubtitleFile(subtitleFile);
            result.setProcessingTimeMs(processingTime);
            result.setExtractorUsed(extractorUsed);
            result.setMessage(String.format("字幕轨道 %d 提取成功，耗时: %dms，使用: %s", trackIndex, processingTime, extractorUsed));

            log.info("字幕轨道 {} 提取成功，耗时: {}ms，使用: {}", trackIndex, processingTime, extractorUsed);

            return result;

        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("字幕轨道提取失败", e);
            throw new AudioConversionException("字幕轨道提取失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            FileUtil.cleanupTempFile(tempSourceFile);
        }
    }

    /**
     * 检查字幕提取功能是否可用（优先FFmpeg，备选JAVE2）
     *
     * @return 是否支持
     */
    public static boolean isSubtitleExtractionSupported() {
        return MediaConverterUtil.isFFmpegAvailable() || MediaConverterUtil.isJaveAvailable();
    }

    /**
     * 检查FFmpeg是否可用
     *
     * @return FFmpeg是否可用
     */
    public static boolean isFFmpegAvailable() {
        return MediaConverterUtil.isFFmpegAvailable();
    }

    /**
     * 检查JAVE2是否可用
     *
     * @return JAVE2是否可用
     */
    public static boolean isJaveAvailable() {
        return MediaConverterUtil.isJaveAvailable();
    }

    /**
     * 获取支持的字幕格式
     *
     * @return 支持的字幕格式数组
     */
    public static String[] getSupportedSubtitleFormats() {
        return new String[]{"SRT", "VTT", "ASS", "SSA", "SUB", "IDX"};
    }



    /**
     * 使用JAVE2提取所有字幕轨道（功能有限）
     */
    private static List<SubtitleExtractionResult.SubtitleFile> extractAllSubtitleTracksWithJave(File videoFile) throws Exception {
        List<SubtitleExtractionResult.SubtitleFile> subtitleFiles = new ArrayList<>();

        try {
            // 注意：JAVE2对字幕提取的支持有限
            // 这里提供一个基础实现，实际项目中可能需要使用FFmpeg命令行
            log.warn("JAVE2对字幕提取的支持有限，建议使用FFmpeg命令行工具进行字幕提取");

            // 尝试提取第一个字幕轨道作为示例
            SubtitleExtractionResult.SubtitleFile subtitleFile = extractSingleSubtitleTrackWithJave(videoFile, 0);
            if (subtitleFile != null) {
                subtitleFiles.add(subtitleFile);
            }

        } catch (Exception e) {
            log.error("提取字幕轨道失败", e);
            throw new Exception("提取字幕轨道失败: " + e.getMessage(), e);
        }

        return subtitleFiles;
    }

    /**
     * 使用JAVE2提取单个字幕轨道（功能有限）
     */
    private static SubtitleExtractionResult.SubtitleFile extractSingleSubtitleTrackWithJave(File videoFile, int trackIndex) throws Exception {
        try {
            // 创建字幕信息
            SubtitleInfo subtitleInfo = new SubtitleInfo();
            subtitleInfo.setTrackIndex(trackIndex);
            subtitleInfo.setLanguage("unknown");
            subtitleInfo.setFormat("srt");
            subtitleInfo.setDefault(trackIndex == 0);

            // 创建临时字幕文件
            File subtitleFile = File.createTempFile("subtitle_" + trackIndex + "_", ".srt");

            // 注意：JAVE2不直接支持字幕提取
            // 这里创建一个占位符文件，实际实现需要使用FFmpeg命令行
            String placeholderContent = "1\n00:00:00,000 --> 00:00:05,000\n字幕提取功能需要FFmpeg支持\n\n";
            Files.write(subtitleFile.toPath(), placeholderContent.getBytes(StandardCharsets.UTF_8));

            // 创建字幕文件对象
            SubtitleExtractionResult.SubtitleFile result = new SubtitleExtractionResult.SubtitleFile();
            result.setSubtitleInfo(subtitleInfo);
            result.setFile(subtitleFile);
            result.setContent(placeholderContent);
            result.setStream(new FileInputStream(subtitleFile));

            log.debug("字幕轨道 {} 提取完成: {}", trackIndex, subtitleFile.getName());

            return result;

        } catch (Exception e) {
            log.error("提取字幕轨道 {} 失败", trackIndex, e);
            throw new Exception("提取字幕轨道失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理临时文件
     */
    public static void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("临时字幕文件已清理: {}", file.getAbsolutePath());
            } catch (IOException e) {
                log.warn("清理临时字幕文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 清理字幕提取结果中的临时文件
     */
    public static void cleanupExtractionResult(SubtitleExtractionResult result) {
        if (result == null) {
            return;
        }

        // 清理字幕文件
        if (result.getSubtitleFiles() != null) {
            for (SubtitleExtractionResult.SubtitleFile subtitleFile : result.getSubtitleFiles()) {
                if (subtitleFile.getFile() != null) {
                    cleanupTempFile(subtitleFile.getFile());
                }
            }
        }

        // 清理临时文件列表
        if (result.getTempFiles() != null) {
            for (File tempFile : result.getTempFiles()) {
                cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 使用FFmpeg提取所有字幕轨道
     */
    private static List<SubtitleExtractionResult.SubtitleFile> extractAllSubtitleTracksWithFFmpeg(File videoFile) throws Exception {
        List<SubtitleExtractionResult.SubtitleFile> subtitleFiles = new ArrayList<>();

        try {
            // 首先检测视频中的字幕轨道数量
            List<SubtitleInfo> subtitleInfos = detectSubtitleTracksWithFFmpeg(videoFile);

            // 提取每个字幕轨道
            for (SubtitleInfo subtitleInfo : subtitleInfos) {
                try {
                    SubtitleExtractionResult.SubtitleFile subtitleFile =
                        extractSingleSubtitleTrackWithFFmpeg(videoFile, subtitleInfo.getTrackIndex());
                    if (subtitleFile != null) {
                        subtitleFiles.add(subtitleFile);
                    }
                } catch (Exception e) {
                    log.warn("提取字幕轨道 {} 失败: {}", subtitleInfo.getTrackIndex(), e.getMessage());
                }
            }

            log.info("FFmpeg字幕提取完成，成功提取 {} 个轨道", subtitleFiles.size());

        } catch (Exception e) {
            log.error("FFmpeg提取字幕轨道失败", e);
            throw new Exception("FFmpeg提取字幕轨道失败: " + e.getMessage(), e);
        }

        return subtitleFiles;
    }

    /**
     * 使用FFmpeg提取单个字幕轨道
     */
    private static SubtitleExtractionResult.SubtitleFile extractSingleSubtitleTrackWithFFmpeg(File videoFile, int trackIndex) throws Exception {
        try {
            // 创建字幕信息
            SubtitleInfo subtitleInfo = new SubtitleInfo();
            subtitleInfo.setTrackIndex(trackIndex);
            subtitleInfo.setLanguage("unknown");
            subtitleInfo.setFormat("srt");
            subtitleInfo.setDefault(trackIndex == 0);

            // 创建临时字幕文件
            File subtitleFile = File.createTempFile("ffmpeg_subtitle_" + trackIndex + "_", ".srt");

            // 构建FFmpeg命令
            List<String> command = new ArrayList<>();
            command.add("ffmpeg");
            command.add("-i");
            command.add(videoFile.getAbsolutePath());
            command.add("-map");
            command.add("0:s:" + trackIndex); // 选择字幕轨道
            command.add("-c:s");
            command.add("srt"); // 输出为SRT格式
            command.add("-y"); // 覆盖输出文件
            command.add(subtitleFile.getAbsolutePath());

            // 执行FFmpeg命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出（用于调试）
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();

            if (exitCode != 0) {
                log.warn("FFmpeg字幕提取警告，退出码: {}，输出: {}", exitCode, output.toString());
                // 某些情况下即使退出码非0，文件也可能成功创建
            }

            // 检查文件是否创建成功且有内容
            if (!subtitleFile.exists() || subtitleFile.length() == 0) {
                throw new Exception("FFmpeg未能成功提取字幕轨道 " + trackIndex);
            }

            // 读取字幕内容
            String content = Files.readString(subtitleFile.toPath(), StandardCharsets.UTF_8);

            // 创建字幕文件对象
            SubtitleExtractionResult.SubtitleFile result = new SubtitleExtractionResult.SubtitleFile();
            result.setSubtitleInfo(subtitleInfo);
            result.setFile(subtitleFile);
            result.setContent(content);
            result.setStream(new FileInputStream(subtitleFile));

            log.debug("FFmpeg字幕轨道 {} 提取完成: {}", trackIndex, subtitleFile.getName());

            return result;

        } catch (Exception e) {
            log.error("FFmpeg提取字幕轨道 {} 失败", trackIndex, e);
            throw new Exception("FFmpeg提取字幕轨道失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg检测视频中的字幕轨道
     */
    private static List<SubtitleInfo> detectSubtitleTracksWithFFmpeg(File videoFile) throws Exception {
        List<SubtitleInfo> subtitleInfos = new ArrayList<>();

        try {
            // 构建FFmpeg命令来检测流信息
            List<String> command = new ArrayList<>();
            command.add("ffprobe");
            command.add("-v");
            command.add("quiet");
            command.add("-print_format");
            command.add("csv");
            command.add("-show_streams");
            command.add("-select_streams");
            command.add("s"); // 只选择字幕流
            command.add(videoFile.getAbsolutePath());

            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出
            List<String> lines = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                }
            }

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                // 解析输出，每行代表一个字幕流
                int trackIndex = 0;
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;

                    SubtitleInfo subtitleInfo = new SubtitleInfo();
                    subtitleInfo.setTrackIndex(trackIndex);
                    subtitleInfo.setLanguage("unknown");
                    subtitleInfo.setFormat("srt");
                    subtitleInfo.setDefault(trackIndex == 0);

                    // 简单解析CSV格式的输出
                    String[] parts = line.split(",");
                    if (parts.length > 2) {
                        // 尝试从输出中提取更多信息
                        for (String part : parts) {
                            if (part.contains("language")) {
                                // 提取语言信息
                                String[] langParts = part.split("=");
                                if (langParts.length > 1) {
                                    subtitleInfo.setLanguage(langParts[1]);
                                }
                            }
                        }
                    }

                    subtitleInfos.add(subtitleInfo);
                    trackIndex++;
                }

                log.debug("检测到 {} 个字幕轨道", subtitleInfos.size());
            } else {
                log.warn("FFprobe检测字幕轨道失败，退出码: {}", exitCode);
                // 如果检测失败，假设有一个字幕轨道
                SubtitleInfo defaultInfo = new SubtitleInfo();
                defaultInfo.setTrackIndex(0);
                defaultInfo.setLanguage("unknown");
                defaultInfo.setFormat("srt");
                defaultInfo.setDefault(true);
                subtitleInfos.add(defaultInfo);
            }

        } catch (Exception e) {
            log.error("FFmpeg检测字幕轨道失败", e);
            // 如果检测失败，假设有一个字幕轨道
            SubtitleInfo defaultInfo = new SubtitleInfo();
            defaultInfo.setTrackIndex(0);
            defaultInfo.setLanguage("unknown");
            defaultInfo.setFormat("srt");
            defaultInfo.setDefault(true);
            subtitleInfos.add(defaultInfo);
        }

        return subtitleInfos;
    }
}
