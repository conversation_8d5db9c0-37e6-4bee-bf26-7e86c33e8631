package org.yixz.service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.converter.SubtitleExtractor;
import org.yixz.common.converter.SubtitleExtractionResult;
import org.yixz.common.util.MediaConverterUtil;

/**
 * 字幕提取服务
 * 统一管理字幕提取功能
 *
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
@Slf4j
@Service
public class SubtitleExtractionService {

    private boolean subtitleExtractionAvailable = false;

    @PostConstruct
    public void init() {
        // 检查字幕提取功能可用性
        subtitleExtractionAvailable = SubtitleExtractor.isSubtitleExtractionSupported();

        // 检查具体的提取器可用性
        boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
        boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

        log.info("字幕提取功能可用性: {}", subtitleExtractionAvailable);
        log.info("FFmpeg可用性: {}", ffmpegAvailable);
        log.info("JAVE2可用性: {}", javeAvailable);

        if (!subtitleExtractionAvailable) {
            log.warn("字幕提取功能不可用！请安装FFmpeg或确保JAVE2依赖正确");
        } else {
            if (ffmpegAvailable) {
                log.info("字幕提取功能已就绪，优先使用FFmpeg，支持格式: {}", String.join(", ", SubtitleExtractor.getSupportedSubtitleFormats()));
            } else {
                log.info("字幕提取功能已就绪，使用JAVE2（功能有限），支持格式: {}", String.join(", ", SubtitleExtractor.getSupportedSubtitleFormats()));
            }
        }
    }

    /**
     * 从视频文件中提取所有字幕
     * 
     * @param videoFile 视频文件
     * @return 字幕提取结果
     */
    public SubtitleExtractionResult extractSubtitles(MultipartFile videoFile) {
        try {
            log.info("开始提取字幕，文件: {}", videoFile.getOriginalFilename());
            
            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitles(videoFile);
            
        } catch (AudioConversionException e) {
            // 字幕提取异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("字幕提取服务异常", e);
            throw new AudioConversionException("字幕提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 从视频文件中提取指定字幕轨道
     * 
     * @param videoFile 视频文件
     * @param trackIndex 字幕轨道索引
     * @return 字幕提取结果
     */
    public SubtitleExtractionResult extractSubtitleTrack(MultipartFile videoFile, int trackIndex) {
        try {
            log.info("开始提取字幕轨道，文件: {}, 轨道索引: {}", videoFile.getOriginalFilename(), trackIndex);
            
            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitleTrack(videoFile, trackIndex);
            
        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("字幕轨道提取服务异常", e);
            throw new AudioConversionException("字幕轨道提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查视频文件是否包含字幕
     *
     * @param videoFile 视频文件
     * @return 是否包含字幕
     */
    public boolean hasSubtitles(MultipartFile videoFile) {
        try {
            log.debug("检查视频文件是否包含字幕: {}", videoFile.getOriginalFilename());

            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                log.warn("字幕提取功能不可用，无法检查字幕");
                return false;
            }

            // 使用通用工具类进行检查
            return org.yixz.common.util.SubtitleUtils.hasSubtitles(videoFile);

        } catch (Exception e) {
            log.debug("检查字幕失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 批量提取字幕（处理多个视频文件）
     *
     * @param videoFiles 视频文件数组
     * @return 字幕提取结果列表
     */
    public java.util.List<SubtitleExtractionResult> batchExtractSubtitles(MultipartFile[] videoFiles) {
        try {
            log.info("开始批量提取字幕，文件数量: {}", videoFiles != null ? videoFiles.length : 0);

            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }

            // 使用通用工具类进行批量处理
            return org.yixz.common.util.SubtitleUtils.batchExtractSubtitles(videoFiles);

        } catch (AudioConversionException e) {
            // 字幕提取异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("批量字幕提取服务异常", e);
            throw new AudioConversionException("批量字幕提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 从目录中批量提取所有视频文件的字幕
     *
     * @param directoryPath 目录路径
     * @param recursive 是否递归搜索子目录
     * @return 字幕提取结果列表
     */
    public java.util.List<SubtitleExtractionResult> extractSubtitlesFromDirectory(String directoryPath, boolean recursive) {
        try {
            log.info("开始从目录提取字幕，路径: {}, 递归: {}", directoryPath, recursive);

            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }

            // 使用通用工具类进行目录处理
            return org.yixz.common.util.SubtitleUtils.extractSubtitlesFromDirectory(
                    java.nio.file.Paths.get(directoryPath), recursive);

        } catch (AudioConversionException e) {
            // 字幕提取异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("目录字幕提取服务异常", e);
            throw new AudioConversionException("目录字幕提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证字幕内容的完整性
     *
     * @param result 字幕提取结果
     * @return 验证结果
     */
    public SubtitleValidationResult validateSubtitleContent(SubtitleExtractionResult result) {
        SubtitleValidationResult validation = new SubtitleValidationResult();

        if (result == null || !result.isSuccess()) {
            validation.setValid(false);
            validation.addIssue("字幕提取结果无效或提取失败");
            return validation;
        }

        java.util.List<SubtitleExtractionResult.SubtitleFile> subtitleFiles = result.getSubtitleFiles();
        if (subtitleFiles == null || subtitleFiles.isEmpty()) {
            validation.setValid(false);
            validation.addIssue("没有找到字幕文件");
            return validation;
        }

        // 验证每个字幕文件
        for (SubtitleExtractionResult.SubtitleFile subtitleFile : subtitleFiles) {
            validateSingleSubtitleFile(subtitleFile, validation);
        }

        // 如果没有发现问题，标记为有效
        if (validation.getIssues().isEmpty()) {
            validation.setValid(true);
        }

        log.info("字幕内容验证完成，有效: {}, 问题数: {}", validation.isValid(), validation.getIssues().size());
        return validation;
    }

    /**
     * 获取支持的字幕格式
     *
     * @return 支持的字幕格式数组
     */
    public String[] getSupportedSubtitleFormats() {
        return SubtitleExtractor.getSupportedSubtitleFormats();
    }

    /**
     * 清理字幕提取结果中的临时文件
     * 
     * @param result 字幕提取结果
     */
    public void cleanupExtractionResult(SubtitleExtractionResult result) {
        SubtitleExtractor.cleanupExtractionResult(result);
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getServiceStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setSubtitleExtractionAvailable(subtitleExtractionAvailable);
        status.setSupportedFormats(SubtitleExtractor.getSupportedSubtitleFormats());

        boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
        boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

        if (subtitleExtractionAvailable) {
            if (ffmpegAvailable) {
                status.setExtractorType("FFmpeg");
                status.setMessage("字幕提取功能已就绪，优先使用FFmpeg");
            } else if (javeAvailable) {
                status.setExtractorType("JAVE2");
                status.setMessage("字幕提取功能已就绪，使用JAVE2（功能有限）");
            }
        } else {
            status.setExtractorType("无");
            status.setMessage("字幕提取功能不可用，请安装FFmpeg或确保JAVE2依赖正确");
        }

        return status;
    }

    /**
     * 验证单个字幕文件
     *
     * @param subtitleFile 字幕文件
     * @param validation 验证结果
     */
    private void validateSingleSubtitleFile(SubtitleExtractionResult.SubtitleFile subtitleFile, SubtitleValidationResult validation) {
        if (subtitleFile == null) {
            validation.addIssue("字幕文件对象为null");
            return;
        }

        // 检查字幕信息
        if (subtitleFile.getSubtitleInfo() == null) {
            validation.addIssue("字幕信息缺失");
        }

        // 检查文件
        if (subtitleFile.getFile() == null) {
            validation.addIssue("字幕文件路径缺失");
        } else if (!subtitleFile.getFile().exists()) {
            validation.addIssue("字幕文件不存在: " + subtitleFile.getFile().getName());
        } else if (subtitleFile.getFile().length() == 0) {
            validation.addIssue("字幕文件为空: " + subtitleFile.getFile().getName());
        }

        // 检查内容
        String content = subtitleFile.getContent();
        if (content == null || content.trim().isEmpty()) {
            validation.addIssue("字幕内容为空");
        } else {
            // 简单的内容格式验证
            validateSubtitleFormat(content, validation);
        }
    }

    /**
     * 验证字幕格式
     *
     * @param content 字幕内容
     * @param validation 验证结果
     */
    private void validateSubtitleFormat(String content, SubtitleValidationResult validation) {
        // 检查是否包含时间戳（SRT格式）
        if (content.contains("-->")) {
            // SRT格式验证
            if (!content.matches("(?s).*\\d{2}:\\d{2}:\\d{2},\\d{3}\\s*-->\\s*\\d{2}:\\d{2}:\\d{2},\\d{3}.*")) {
                validation.addIssue("SRT格式时间戳格式不正确");
            }
        } else if (content.contains("WEBVTT")) {
            // VTT格式验证
            if (!content.startsWith("WEBVTT")) {
                validation.addIssue("VTT格式头部缺失");
            }
        } else {
            // 其他格式的基本检查
            if (content.length() < 10) {
                validation.addIssue("字幕内容过短，可能不完整");
            }
        }
    }

    /**
     * 服务状态类
     */
    public static class ServiceStatus {
        private boolean subtitleExtractionAvailable;
        private String extractorType;
        private String message;
        private String[] supportedFormats;

        // Getters and Setters
        public boolean isSubtitleExtractionAvailable() {
            return subtitleExtractionAvailable;
        }

        public void setSubtitleExtractionAvailable(boolean subtitleExtractionAvailable) {
            this.subtitleExtractionAvailable = subtitleExtractionAvailable;
        }

        public String getExtractorType() {
            return extractorType;
        }

        public void setExtractorType(String extractorType) {
            this.extractorType = extractorType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String[] getSupportedFormats() {
            return supportedFormats;
        }

        public void setSupportedFormats(String[] supportedFormats) {
            this.supportedFormats = supportedFormats;
        }

        @Override
        public String toString() {
            return String.format("ServiceStatus{available=%s, extractor='%s', message='%s', formats=%s}",
                    subtitleExtractionAvailable, extractorType, message,
                    supportedFormats != null ? String.join(",", supportedFormats) : "none");
        }
    }

    /**
     * 字幕验证结果类
     */
    public static class SubtitleValidationResult {
        private boolean valid;
        private java.util.List<String> issues;

        public SubtitleValidationResult() {
            this.valid = false;
            this.issues = new java.util.ArrayList<>();
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public java.util.List<String> getIssues() {
            return issues;
        }

        public void setIssues(java.util.List<String> issues) {
            this.issues = issues;
        }

        public void addIssue(String issue) {
            if (this.issues == null) {
                this.issues = new java.util.ArrayList<>();
            }
            this.issues.add(issue);
        }

        @Override
        public String toString() {
            return String.format("SubtitleValidationResult{valid=%s, issues=%s}",
                    valid, issues != null ? issues.size() : 0);
        }
    }
}
